# Apify Configuration
APIFY_API_TOKEN=your_apify_api_token_here
APIFY_CHANNEL_SCRAPER_ID=1p1aa7gcSydPkAE0d
APIFY_TRANSCRIPT_SCRAPER_ID=faVsWy9VTSNVIhWpR

# YouTube Channel Configuration
HUBERMAN_CHANNEL_URL=https://www.youtube.com/@hubermanlab
HUBERMAN_CHANNEL_ID=UC2D2CMWXMOVWx7giW1n3LIg
YOUTUBE_API_KEY=your_youtube_data_api_v3_key_here

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/huberman_health_ai
DB_HOST=localhost
DB_PORT=5432
DB_NAME=huberman_health_ai
DB_USER=username
DB_PASSWORD=password

# Processing Configuration
BATCH_SIZE=50
MAX_CONCURRENT_REQUESTS=5
RETRY_ATTEMPTS=3
RETRY_DELAY=1000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/data-pipeline.log

# Data Storage
DATA_OUTPUT_DIR=./data/output
TEMP_DATA_DIR=./data/temp

# Processing Options
ENABLE_TRANSCRIPT_CLEANING=true
ENABLE_TIMESTAMP_EXTRACTION=true
ENABLE_TOPIC_EXTRACTION=true
