#!/usr/bin/env node

import { YouTubeApiScraper } from './src/scrapers/youtubeApiScraper.js';
import { createLogger } from './src/utils/logger.js';
import dotenv from 'dotenv';

dotenv.config();

const logger = createLogger('TestYouTubeAPI');

async function testYouTubeApiScraper() {
    logger.info('🧪 Testing YouTube Data API Scraper...');
    
    try {
        // Check environment variables
        if (!process.env.YOUTUBE_API_KEY) {
            throw new Error('❌ YOUTUBE_API_KEY not found in environment variables');
        }
        
        logger.info('✅ YOUTUBE_API_KEY found');
        
        // Test database connection
        const { DatabaseManager } = await import('./src/utils/database.js');
        const db = new DatabaseManager();
        
        try {
            await db.testConnection();
            logger.info('✅ Database connection successful');
        } catch (error) {
            logger.error('❌ Database connection failed:', error.message);
            logger.info('💡 Make sure PostgreSQL is running and DATABASE_URL is correct');
            logger.info('💡 For now, we\'ll test the API without database storage');
        }
        
        // Test YouTube API scraper initialization
        const scraper = new YouTubeApiScraper();
        logger.info('✅ YouTube API scraper initialized');
        
        // Test API connection with a small request
        logger.info('🔍 Testing YouTube API connection...');
        
        const testVideos = await scraper.getAllChannelVideos(2); // Just get 2 videos for testing
        
        if (testVideos && testVideos.length > 0) {
            logger.info(`✅ Successfully fetched ${testVideos.length} test videos`);
            
            const video = testVideos[0];
            logger.info('📹 Sample video data:');
            logger.info(`   Title: ${video.snippet.title}`);
            logger.info(`   ID: ${video.id}`);
            logger.info(`   Published: ${video.snippet.publishedAt}`);
            logger.info(`   Duration: ${video.contentDetails?.duration}`);
            logger.info(`   Views: ${parseInt(video.statistics?.viewCount || 0).toLocaleString()}`);
            logger.info(`   Likes: ${parseInt(video.statistics?.likeCount || 0).toLocaleString()}`);
        } else {
            logger.error('❌ No videos fetched from API');
            return;
        }
        
        // Show quota usage info
        await scraper.checkQuotaUsage();
        
        logger.info('🎉 All tests passed! YouTube Data API is working.');
        logger.info('💡 Run "node src/scrapers/youtubeApiScraper.js" to start scraping');
        
        await db.close();
        
    } catch (error) {
        logger.error('❌ Test failed:', error.message);
        
        if (error.response) {
            logger.error('API Error Details:', {
                status: error.response.status,
                statusText: error.response.statusText,
                data: error.response.data
            });
            
            if (error.response.status === 403) {
                logger.info('🔧 Troubleshooting 403 Forbidden:');
                logger.info('1. Check if your YouTube Data API key is correct');
                logger.info('2. Make sure YouTube Data API v3 is enabled in Google Cloud Console');
                logger.info('3. Verify your API key has proper permissions');
                logger.info('4. Check if you have exceeded your daily quota');
            } else if (error.response.status === 400) {
                logger.info('🔧 Troubleshooting 400 Bad Request:');
                logger.info('1. Check if the channel ID is correct');
                logger.info('2. Verify API parameters are properly formatted');
            }
        } else {
            logger.info('🔧 General troubleshooting:');
            logger.info('1. Make sure your YOUTUBE_API_KEY is set in the .env file');
            logger.info('2. Check your internet connectivity');
            logger.info('3. Verify the YouTube Data API v3 is enabled');
        }
    }
}

testYouTubeApiScraper();
