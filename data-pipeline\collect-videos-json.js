#!/usr/bin/env node

import axios from 'axios';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

console.log('🚀 Collecting Huberman Lab videos to JSON...');

class YouTubeCollector {
    constructor() {
        this.apiKey = process.env.YOUTUBE_API_KEY;
        this.baseUrl = 'https://www.googleapis.com/youtube/v3';
        this.channelId = process.env.HUBERMAN_CHANNEL_ID || 'UC2D2CMWXMOVWx7giW1n3LIg';
        
        if (!this.apiKey) {
            throw new Error('YOUTUBE_API_KEY not found in environment variables');
        }
    }

    async collectAllVideos(maxResults = 1000) {
        const allVideos = [];
        let nextPageToken = null;
        const maxResultsPerPage = 50;

        try {
            do {
                console.log(`📹 Fetching videos page... (current total: ${allVideos.length})`);
                
                const response = await this.getChannelVideosPage(maxResultsPerPage, nextPageToken);
                
                if (response.items && response.items.length > 0) {
                    // Get detailed video information
                    const videoIds = response.items.map(item => item.id.videoId).filter(Boolean);
                    const detailedVideos = await this.getVideoDetails(videoIds);
                    
                    allVideos.push(...detailedVideos);
                    nextPageToken = response.nextPageToken;
                    
                    console.log(`   ✅ Fetched ${detailedVideos.length} videos, total: ${allVideos.length}`);
                } else {
                    break;
                }

                // Respect rate limits
                await this.delay(200);

            } while (nextPageToken && allVideos.length < maxResults);

            return allVideos.slice(0, maxResults);

        } catch (error) {
            console.error('❌ Error fetching videos:', error.message);
            throw error;
        }
    }

    async getChannelVideosPage(maxResults = 50, pageToken = null) {
        const params = {
            part: 'id,snippet',
            channelId: this.channelId,
            type: 'video',
            order: 'date',
            maxResults: maxResults,
            key: this.apiKey
        };

        if (pageToken) {
            params.pageToken = pageToken;
        }

        const response = await axios.get(`${this.baseUrl}/search`, { params });
        return response.data;
    }

    async getVideoDetails(videoIds) {
        if (!videoIds || videoIds.length === 0) return [];

        try {
            const params = {
                part: 'snippet,contentDetails,statistics',
                id: videoIds.join(','),
                key: this.apiKey
            };

            const response = await axios.get(`${this.baseUrl}/videos`, { params });
            return response.data.items || [];

        } catch (error) {
            console.error('❌ Error fetching video details:', error.message);
            return [];
        }
    }

    processVideoData(rawVideos) {
        return rawVideos.map(video => ({
            youtube_id: video.id,
            title: this.cleanText(video.snippet.title),
            description: this.cleanText(video.snippet.description),
            published_at: video.snippet.publishedAt,
            duration_seconds: this.parseDuration(video.contentDetails?.duration),
            view_count: parseInt(video.statistics?.viewCount) || 0,
            like_count: parseInt(video.statistics?.likeCount) || 0,
            thumbnail_url: this.getBestThumbnail(video.snippet.thumbnails),
            url: `https://www.youtube.com/watch?v=${video.id}`,
            channel_id: video.snippet.channelId,
            tags: video.snippet.tags || []
        }));
    }

    cleanText(text) {
        if (!text) return '';
        return text.replace(/\s+/g, ' ').replace(/[\r\n]+/g, ' ').trim();
    }

    parseDuration(duration) {
        if (!duration) return 0;
        const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
        if (match) {
            const hours = parseInt(match[1]) || 0;
            const minutes = parseInt(match[2]) || 0;
            const seconds = parseInt(match[3]) || 0;
            return hours * 3600 + minutes * 60 + seconds;
        }
        return 0;
    }

    getBestThumbnail(thumbnails) {
        if (!thumbnails) return '';
        const qualities = ['maxres', 'standard', 'high', 'medium', 'default'];
        for (const quality of qualities) {
            if (thumbnails[quality]) {
                return thumbnails[quality].url;
            }
        }
        return '';
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    generateStats(videos) {
        const totalVideos = videos.length;
        const totalDuration = videos.reduce((sum, v) => sum + v.duration_seconds, 0);
        const totalViews = videos.reduce((sum, v) => sum + v.view_count, 0);
        const dates = videos.map(v => new Date(v.published_at)).sort();
        
        return {
            totalVideos,
            totalHours: Math.round(totalDuration / 3600),
            averageDuration: Math.round(totalDuration / totalVideos / 60), // minutes
            totalViews: totalViews.toLocaleString(),
            averageViews: Math.round(totalViews / totalVideos).toLocaleString(),
            oldestVideo: dates[0]?.toLocaleDateString(),
            latestVideo: dates[dates.length - 1]?.toLocaleDateString()
        };
    }
}

// Main execution
async function main() {
    try {
        const collector = new YouTubeCollector();
        
        console.log('💡 YouTube API Quota Usage Estimate:');
        console.log('   ~100 units per 50 videos (search)');
        console.log('   ~1 unit per video (details)');
        console.log('   Daily limit: 10,000 units');
        
        // Collect all videos
        const rawVideos = await collector.collectAllVideos(400); // Collect all ~392 videos
        console.log(`\n✅ Collected ${rawVideos.length} raw videos`);
        
        // Process the data
        const processedVideos = collector.processVideoData(rawVideos);
        console.log(`✅ Processed ${processedVideos.length} videos`);
        
        // Generate statistics
        const stats = collector.generateStats(processedVideos);
        
        // Save to JSON file
        const outputDir = './data/output';
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `huberman-videos-${timestamp}.json`;
        const filepath = path.join(outputDir, filename);
        
        const output = {
            metadata: {
                collected_at: new Date().toISOString(),
                total_videos: processedVideos.length,
                channel_id: collector.channelId,
                statistics: stats
            },
            videos: processedVideos
        };
        
        fs.writeFileSync(filepath, JSON.stringify(output, null, 2));
        
        console.log(`\n💾 Data saved to: ${filepath}`);
        console.log('\n📊 Collection Statistics:');
        console.log(`   Total Videos: ${stats.totalVideos}`);
        console.log(`   Total Hours: ${stats.totalHours}`);
        console.log(`   Average Duration: ${stats.averageDuration} minutes`);
        console.log(`   Total Views: ${stats.totalViews}`);
        console.log(`   Average Views: ${stats.averageViews}`);
        console.log(`   Date Range: ${stats.oldestVideo} to ${stats.latestVideo}`);
        
        console.log('\n🎉 Video collection completed successfully!');
        console.log('💡 Next: Import this data into the database');
        
    } catch (error) {
        console.error('❌ Collection failed:', error.message);
        process.exit(1);
    }
}

main();
