#!/usr/bin/env node

import { ApifyClient } from 'apify-client';
import dotenv from 'dotenv';

dotenv.config();

async function testApifyConnection() {
    console.log('🧪 Testing Apify Connection...');
    
    try {
        // Check environment variables
        if (!process.env.APIFY_API_TOKEN) {
            throw new Error('❌ APIFY_API_TOKEN not found in environment variables');
        }
        
        console.log('✅ APIFY_API_TOKEN found');
        
        // Initialize Apify client
        const apifyClient = new ApifyClient({
            token: process.env.APIFY_API_TOKEN,
        });
        
        console.log('✅ Apify client initialized');
        
        // Test API connection by getting user info
        try {
            const user = await apifyClient.user().get();
            console.log(`✅ Connected to Apify as: ${user.username}`);
            console.log(`   Plan: ${user.plan}`);
            console.log(`   Monthly usage: ${user.usageMonthly?.datasetWrites || 0} dataset writes`);
        } catch (error) {
            console.log('⚠️  Could not get user info, but API token seems valid');
        }
        
        // Test the YouTube Channel Scraper actor
        const actorId = process.env.APIFY_CHANNEL_SCRAPER_ID || '1p1aa7gcSydPkAE0d';
        console.log(`🔍 Testing YouTube Channel Scraper actor: ${actorId}`);
        
        try {
            const actor = await apifyClient.actor(actorId).get();
            console.log(`✅ Actor found: ${actor.name}`);
            console.log(`   Description: ${actor.description?.substring(0, 100)}...`);
            console.log(`   Last run: ${actor.stats?.lastRunStartedAt || 'Never'}`);
        } catch (error) {
            console.log(`❌ Could not access actor ${actorId}:`, error.message);
            return;
        }
        
        // Test with a small scraping job (just 1 video)
        console.log('🚀 Testing small scraping job...');
        
        const input = {
            startUrls: [process.env.HUBERMAN_CHANNEL_URL || 'https://www.youtube.com/@hubermanlab'],
            maxResults: 1, // Just test with 1 video
            includeVideoDetails: true,
            includeComments: false,
            includeSubtitles: false,
            sortBy: 'newest'
        };
        
        console.log('📝 Input configuration:', JSON.stringify(input, null, 2));
        
        const run = await apifyClient.actor(actorId).call(input, {
            timeout: 60000, // 1 minute timeout for test
        });
        
        console.log(`🎯 Test run completed with status: ${run.status}`);
        
        if (run.status === 'SUCCEEDED') {
            const { items } = await apifyClient.dataset(run.defaultDatasetId).listItems();
            console.log(`✅ Successfully scraped ${items.length} video(s)`);
            
            if (items.length > 0) {
                const video = items[0];
                console.log('📹 Sample video data:');
                console.log(`   Title: ${video.title}`);
                console.log(`   ID: ${video.id}`);
                console.log(`   Published: ${video.publishedAt}`);
                console.log(`   Duration: ${video.duration}`);
                console.log(`   Views: ${video.viewCount?.toLocaleString()}`);
            }
        } else {
            console.log(`❌ Test run failed with status: ${run.status}`);
            if (run.statusMessage) {
                console.log(`   Error: ${run.statusMessage}`);
            }
        }
        
        console.log('🎉 Apify test completed successfully!');
        console.log('💡 Your Apify setup is working. You can now run the full video scraper.');
        
    } catch (error) {
        console.error('❌ Apify test failed:', error.message);
        console.log('\n🔧 Troubleshooting tips:');
        console.log('1. Make sure your APIFY_API_TOKEN is correct');
        console.log('2. Check that you have sufficient Apify credits');
        console.log('3. Verify the actor ID is correct');
        console.log('4. Ensure you have internet connectivity');
    }
}

testApifyConnection();
