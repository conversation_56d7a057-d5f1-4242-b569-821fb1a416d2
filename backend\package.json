{"name": "huberman-health-ai-backend", "version": "1.0.0", "description": "MCP Server and API for Huberman Health AI Assistant", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "pgvector": "^0.1.8", "redis": "^4.6.10", "axios": "^1.6.0", "openai": "^4.20.1", "winston": "^3.11.0", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "rate-limiter-flexible": "^2.4.2", "prom-client": "^15.0.0", "@xenova/transformers": "^2.6.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "@babel/preset-env": "^7.23.3", "@babel/core": "^7.23.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mcp-server", "health-ai", "huberman", "semantic-search"], "author": "Your Name", "license": "MIT"}