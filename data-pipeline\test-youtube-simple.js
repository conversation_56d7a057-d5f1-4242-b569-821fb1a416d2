#!/usr/bin/env node

import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

async function testYouTubeApiSimple() {
    console.log('🧪 Testing YouTube Data API (Simple)...');
    
    try {
        // Check environment variables
        if (!process.env.YOUTUBE_API_KEY) {
            console.log('❌ YOUTUBE_API_KEY not found in environment variables');
            console.log('💡 Please add YOUTUBE_API_KEY=your_key_here to data-pipeline/.env file');
            return;
        }
        
        console.log('✅ YOUTUBE_API_KEY found');
        
        const apiKey = process.env.YOUTUBE_API_KEY;
        const channelId = process.env.HUBERMAN_CHANNEL_ID || 'UC2D2CMWXMOVWx7giW1n3LIg';
        const baseUrl = 'https://www.googleapis.com/youtube/v3';
        
        // Test 1: Get channel info
        console.log('🔍 Testing channel info API...');
        
        try {
            const channelResponse = await axios.get(`${baseUrl}/channels`, {
                params: {
                    part: 'snippet,statistics',
                    id: channelId,
                    key: apiKey
                }
            });
            
            if (channelResponse.data.items && channelResponse.data.items.length > 0) {
                const channel = channelResponse.data.items[0];
                console.log('✅ Channel info retrieved successfully:');
                console.log(`   Channel: ${channel.snippet.title}`);
                console.log(`   Subscribers: ${parseInt(channel.statistics.subscriberCount).toLocaleString()}`);
                console.log(`   Total Videos: ${parseInt(channel.statistics.videoCount).toLocaleString()}`);
                console.log(`   Total Views: ${parseInt(channel.statistics.viewCount).toLocaleString()}`);
            } else {
                console.log('❌ No channel data found');
                return;
            }
        } catch (error) {
            console.log('❌ Channel info API failed:', error.response?.data || error.message);
            return;
        }
        
        // Test 2: Get recent videos
        console.log('🔍 Testing search API for recent videos...');
        
        try {
            const searchResponse = await axios.get(`${baseUrl}/search`, {
                params: {
                    part: 'id,snippet',
                    channelId: channelId,
                    type: 'video',
                    order: 'date',
                    maxResults: 3,
                    key: apiKey
                }
            });
            
            if (searchResponse.data.items && searchResponse.data.items.length > 0) {
                console.log(`✅ Found ${searchResponse.data.items.length} recent videos:`);
                
                searchResponse.data.items.forEach((item, index) => {
                    console.log(`   ${index + 1}. ${item.snippet.title}`);
                    console.log(`      Published: ${new Date(item.snippet.publishedAt).toLocaleDateString()}`);
                    console.log(`      Video ID: ${item.id.videoId}`);
                });
                
                // Test 3: Get detailed video info
                console.log('🔍 Testing video details API...');
                
                const videoIds = searchResponse.data.items.map(item => item.id.videoId);
                const videoResponse = await axios.get(`${baseUrl}/videos`, {
                    params: {
                        part: 'snippet,contentDetails,statistics',
                        id: videoIds.join(','),
                        key: apiKey
                    }
                });
                
                if (videoResponse.data.items && videoResponse.data.items.length > 0) {
                    const video = videoResponse.data.items[0];
                    console.log('✅ Video details retrieved successfully:');
                    console.log(`   Title: ${video.snippet.title}`);
                    console.log(`   Duration: ${video.contentDetails.duration}`);
                    console.log(`   Views: ${parseInt(video.statistics.viewCount).toLocaleString()}`);
                    console.log(`   Likes: ${parseInt(video.statistics.likeCount || 0).toLocaleString()}`);
                }
                
            } else {
                console.log('❌ No videos found in search results');
                return;
            }
        } catch (error) {
            console.log('❌ Search API failed:', error.response?.data || error.message);
            return;
        }
        
        // Show quota usage estimate
        console.log('\n💡 YouTube API Quota Usage Estimate:');
        console.log('   Channel info: 1 unit');
        console.log('   Search (3 videos): 100 units');
        console.log('   Video details (3 videos): 3 units');
        console.log('   Total used in this test: ~104 units');
        console.log('   Daily quota limit: 10,000 units (free tier)');
        console.log('   Remaining quota: ~9,896 units');
        
        console.log('\n🎉 All YouTube API tests passed!');
        console.log('💡 Your YouTube Data API v3 setup is working correctly.');
        console.log('💡 You can now run the full video scraper.');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        
        if (error.response) {
            console.log('\n🔧 API Error Details:');
            console.log(`   Status: ${error.response.status} ${error.response.statusText}`);
            console.log(`   Error: ${JSON.stringify(error.response.data, null, 2)}`);
            
            if (error.response.status === 403) {
                console.log('\n🔧 Troubleshooting 403 Forbidden:');
                console.log('1. Check if your YouTube Data API key is correct');
                console.log('2. Make sure YouTube Data API v3 is enabled in Google Cloud Console');
                console.log('3. Verify your API key has proper permissions');
                console.log('4. Check if you have exceeded your daily quota');
            } else if (error.response.status === 400) {
                console.log('\n🔧 Troubleshooting 400 Bad Request:');
                console.log('1. Check if the channel ID is correct');
                console.log('2. Verify API parameters are properly formatted');
            }
        }
    }
}

testYouTubeApiSimple();
