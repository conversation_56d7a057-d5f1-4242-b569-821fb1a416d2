-- Enable pgvector extension for vector similarity search
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create database schema for Huberman Health AI Assistant

-- Videos table - stores YouTube video metadata
CREATE TABLE videos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    youtube_id VARCHAR(20) UNIQUE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    published_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_seconds INTEGER,
    view_count BIGINT,
    like_count INTEGER,
    thumbnail_url TEXT,
    url TEXT NOT NULL,
    channel_id VARCHAR(50) DEFAULT 'UC2D2CMWXMOVWx7giW1n3LIg',
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transcripts table - stores full video transcripts
CREATE TABLE transcripts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    video_id UUID NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    full_text TEXT NOT NULL,
    language VARCHAR(10) DEFAULT 'en',
    confidence_score DECIMAL(3,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transcript segments - stores individual transcript segments with timestamps
CREATE TABLE transcript_segments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transcript_id UUID NOT NULL REFERENCES transcripts(id) ON DELETE CASCADE,
    video_id UUID NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    start_time DECIMAL(10,3) NOT NULL, -- seconds with millisecond precision
    end_time DECIMAL(10,3) NOT NULL,
    text TEXT NOT NULL,
    speaker VARCHAR(100),
    confidence_score DECIMAL(3,2),
    embedding vector(384), -- 384-dimensional embeddings for semantic search
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Health topics - categorized health topics for better organization
CREATE TABLE health_topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL UNIQUE,
    category VARCHAR(100),
    description TEXT,
    keywords TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Video topics - many-to-many relationship between videos and health topics
CREATE TABLE video_topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    video_id UUID NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    topic_id UUID NOT NULL REFERENCES health_topics(id) ON DELETE CASCADE,
    relevance_score DECIMAL(3,2) DEFAULT 0.5,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(video_id, topic_id)
);

-- User queries - stores user search queries for analytics
CREATE TABLE user_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_text TEXT NOT NULL,
    query_embedding vector(384),
    user_ip VARCHAR(45),
    user_agent TEXT,
    response_time_ms INTEGER,
    results_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Query results - stores which videos/segments were returned for queries
CREATE TABLE query_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_id UUID NOT NULL REFERENCES user_queries(id) ON DELETE CASCADE,
    video_id UUID NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    segment_id UUID REFERENCES transcript_segments(id) ON DELETE CASCADE,
    similarity_score DECIMAL(5,4),
    rank_position INTEGER,
    clicked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance optimization

-- Video indexes
CREATE INDEX idx_videos_youtube_id ON videos(youtube_id);
CREATE INDEX idx_videos_published_at ON videos(published_at DESC);
CREATE INDEX idx_videos_title_gin ON videos USING gin(to_tsvector('english', title));
CREATE INDEX idx_videos_description_gin ON videos USING gin(to_tsvector('english', description));

-- Transcript indexes
CREATE INDEX idx_transcripts_video_id ON transcripts(video_id);
CREATE INDEX idx_transcripts_full_text_gin ON transcripts USING gin(to_tsvector('english', full_text));

-- Transcript segment indexes
CREATE INDEX idx_transcript_segments_video_id ON transcript_segments(video_id);
CREATE INDEX idx_transcript_segments_transcript_id ON transcript_segments(transcript_id);
CREATE INDEX idx_transcript_segments_start_time ON transcript_segments(start_time);
CREATE INDEX idx_transcript_segments_text_gin ON transcript_segments USING gin(to_tsvector('english', text));

-- Vector similarity search index (HNSW for fast approximate nearest neighbor search)
CREATE INDEX idx_transcript_segments_embedding ON transcript_segments USING hnsw (embedding vector_cosine_ops);

-- Health topics indexes
CREATE INDEX idx_health_topics_name ON health_topics(name);
CREATE INDEX idx_health_topics_category ON health_topics(category);
CREATE INDEX idx_health_topics_keywords_gin ON health_topics USING gin(keywords);

-- Video topics indexes
CREATE INDEX idx_video_topics_video_id ON video_topics(video_id);
CREATE INDEX idx_video_topics_topic_id ON video_topics(topic_id);
CREATE INDEX idx_video_topics_relevance ON video_topics(relevance_score DESC);

-- User queries indexes
CREATE INDEX idx_user_queries_created_at ON user_queries(created_at DESC);
CREATE INDEX idx_user_queries_embedding ON user_queries USING hnsw (query_embedding vector_cosine_ops);

-- Query results indexes
CREATE INDEX idx_query_results_query_id ON query_results(query_id);
CREATE INDEX idx_query_results_video_id ON query_results(video_id);
CREATE INDEX idx_query_results_similarity ON query_results(similarity_score DESC);

-- Insert some initial health topics
INSERT INTO health_topics (name, category, description, keywords) VALUES
('Sleep Optimization', 'Sleep', 'Techniques and protocols for improving sleep quality and duration', ARRAY['sleep', 'insomnia', 'circadian rhythm', 'melatonin', 'sleep hygiene']),
('Stress Management', 'Mental Health', 'Methods for managing and reducing stress levels', ARRAY['stress', 'cortisol', 'anxiety', 'breathing', 'meditation']),
('Exercise Performance', 'Fitness', 'Protocols for optimizing physical performance and recovery', ARRAY['exercise', 'workout', 'strength', 'endurance', 'recovery']),
('Nutrition Protocols', 'Nutrition', 'Evidence-based nutritional strategies and protocols', ARRAY['nutrition', 'diet', 'fasting', 'supplements', 'metabolism']),
('Cognitive Enhancement', 'Brain Health', 'Techniques for improving focus, memory, and cognitive function', ARRAY['focus', 'memory', 'nootropics', 'brain', 'concentration']),
('Hormone Optimization', 'Hormones', 'Natural methods for optimizing hormone levels', ARRAY['testosterone', 'estrogen', 'growth hormone', 'insulin', 'thyroid']),
('Pain Management', 'Health', 'Natural approaches to managing chronic and acute pain', ARRAY['pain', 'inflammation', 'chronic pain', 'back pain', 'joint pain']),
('Digestive Health', 'Gut Health', 'Protocols for improving digestive function and gut health', ARRAY['digestion', 'gut', 'microbiome', 'stomach', 'intestinal']);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at timestamps
CREATE TRIGGER update_videos_updated_at BEFORE UPDATE ON videos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_transcripts_updated_at BEFORE UPDATE ON transcripts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
