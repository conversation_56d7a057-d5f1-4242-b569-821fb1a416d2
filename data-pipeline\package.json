{"name": "huberman-health-ai-data-pipeline", "version": "1.0.0", "description": "Data collection and processing pipeline for Huberman Health AI Assistant", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "scrape:videos": "node src/scrapers/videoScraper.js", "scrape:videos-youtube": "node src/scrapers/youtubeApiScraper.js", "scrape:transcripts": "node src/scrapers/transcriptScraper.js", "process:data": "node src/processors/dataProcessor.js", "dev": "nodemon src/index.js", "test": "jest", "test:scraper": "node test-scraper.js", "test:youtube": "node test-youtube-api.js", "status": "node src/index.js status", "lint": "eslint src/"}, "dependencies": {"apify-client": "^2.7.1", "dotenv": "^16.3.1", "pg": "^8.11.3", "winston": "^3.11.0", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "natural": "^6.7.0", "compromise": "^14.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "eslint": "^8.54.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["apify", "data-pipeline", "youtube-scraping", "transcript-processing"], "author": "Your Name", "license": "MIT"}