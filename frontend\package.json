{"name": "huberman-health-ai-frontend", "version": "1.0.0", "description": "Frontend for Huberman Health AI Assistant", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.0", "@tanstack/react-query": "^4.36.1", "react-youtube": "^10.1.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4"}, "engines": {"node": ">=18.0.0"}}