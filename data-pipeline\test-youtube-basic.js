#!/usr/bin/env node

import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

console.log('🧪 Testing YouTube Data API...');

// Check environment variables
if (!process.env.YOUTUBE_API_KEY) {
    console.log('❌ YOUTUBE_API_KEY not found in environment variables');
    console.log('💡 Please add YOUTUBE_API_KEY=your_key_here to data-pipeline/.env file');
    process.exit(1);
}

console.log('✅ YOUTUBE_API_KEY found');

const apiKey = process.env.YOUTUBE_API_KEY;
const channelId = process.env.HUBERMAN_CHANNEL_ID || 'UC2D2CMWXMOVWx7giW1n3LIg';
const baseUrl = 'https://www.googleapis.com/youtube/v3';

try {
    console.log('🔍 Testing YouTube API connection...');
    
    // Test with a simple channel info request
    const response = await axios.get(`${baseUrl}/channels`, {
        params: {
            part: 'snippet,statistics',
            id: channelId,
            key: apiKey
        },
        timeout: 10000 // 10 second timeout
    });
    
    if (response.data.items && response.data.items.length > 0) {
        const channel = response.data.items[0];
        console.log('✅ YouTube API is working!');
        console.log(`   Channel: ${channel.snippet.title}`);
        console.log(`   Subscribers: ${parseInt(channel.statistics.subscriberCount).toLocaleString()}`);
        console.log(`   Total Videos: ${parseInt(channel.statistics.videoCount).toLocaleString()}`);
        console.log(`   Total Views: ${parseInt(channel.statistics.viewCount).toLocaleString()}`);
        
        console.log('\n🎉 YouTube Data API test successful!');
        console.log('💡 You can now run the full video scraper.');
    } else {
        console.log('❌ No channel data found');
    }
    
} catch (error) {
    console.error('❌ YouTube API test failed:', error.message);
    
    if (error.response) {
        console.log(`   Status: ${error.response.status} ${error.response.statusText}`);
        console.log(`   Error: ${JSON.stringify(error.response.data, null, 2)}`);
        
        if (error.response.status === 403) {
            console.log('\n🔧 Troubleshooting 403 Forbidden:');
            console.log('1. Check if your YouTube Data API key is correct');
            console.log('2. Make sure YouTube Data API v3 is enabled in Google Cloud Console');
            console.log('3. Verify your API key has proper permissions');
            console.log('4. Check if you have exceeded your daily quota');
        }
    }
}
