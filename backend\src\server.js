#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { createLogger } from './utils/logger.js';
import { DatabaseManager } from './utils/database.js';
import { HealthQueryProcessor } from './services/healthQueryProcessor.js';
import { SemanticSearchService } from './services/semanticSearchService.js';
import { MetricsService } from './services/metricsService.js';
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js';
import { rateLimiter } from './middleware/rateLimiter.js';

// Import routes
import healthRoutes from './routes/health.js';
import videoRoutes from './routes/videos.js';
import searchRoutes from './routes/search.js';
import metricsRoutes from './routes/metrics.js';

dotenv.config();

const logger = createLogger('MCPServer');
const app = express();
const PORT = process.env.PORT || 3001;
const MCP_PORT = process.env.MCP_SERVER_PORT || 3002;

class MCPServer {
    constructor() {
        this.app = express();
        this.db = new DatabaseManager();
        this.healthQueryProcessor = new HealthQueryProcessor();
        this.semanticSearchService = new SemanticSearchService();
        this.metricsService = new MetricsService();
        
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
    }

    setupMiddleware() {
        // Security middleware
        this.app.use(helmet({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
        }));

        // CORS configuration
        this.app.use(cors({
            origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
            credentials: true,
        }));

        // Body parsing middleware
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

        // Rate limiting
        this.app.use(rateLimiter);

        // Request logging
        this.app.use((req, res, next) => {
            logger.info(`${req.method} ${req.path}`, {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                timestamp: new Date().toISOString()
            });
            next();
        });

        // Metrics collection
        this.app.use((req, res, next) => {
            const start = Date.now();
            res.on('finish', () => {
                const duration = Date.now() - start;
                this.metricsService.recordRequest(req.method, req.path, res.statusCode, duration);
            });
            next();
        });
    }

    setupRoutes() {
        // Health check endpoint
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: process.env.npm_package_version || '1.0.0',
                environment: process.env.NODE_ENV || 'development'
            });
        });

        // API routes
        this.app.use('/api/health', healthRoutes);
        this.app.use('/api/videos', videoRoutes);
        this.app.use('/api/search', searchRoutes);
        this.app.use('/api/metrics', metricsRoutes);

        // MCP-specific endpoints
        this.app.post('/api/query', this.handleHealthQuery.bind(this));
        this.app.get('/api/recommendations/:videoId', this.getVideoRecommendations.bind(this));
        this.app.post('/api/semantic-search', this.handleSemanticSearch.bind(this));
    }

    setupErrorHandling() {
        // 404 handler
        this.app.use(notFoundHandler);

        // Global error handler
        this.app.use(errorHandler);
    }

    async handleHealthQuery(req, res) {
        try {
            const { query, userId, preferences } = req.body;

            if (!query || typeof query !== 'string') {
                return res.status(400).json({
                    error: 'Query is required and must be a string',
                    code: 'INVALID_QUERY'
                });
            }

            logger.info('Processing health query', { query, userId });

            // Process the health query
            const processedQuery = await this.healthQueryProcessor.processQuery(query);
            
            // Perform semantic search
            const searchResults = await this.semanticSearchService.search(
                processedQuery.searchTerms,
                processedQuery.healthTopics,
                { limit: 10, userId, preferences }
            );

            // Format response
            const response = {
                query: query,
                processedQuery: processedQuery,
                results: searchResults.videos,
                totalResults: searchResults.total,
                processingTime: searchResults.processingTime,
                recommendations: searchResults.recommendations,
                timestamp: new Date().toISOString()
            };

            // Record metrics
            this.metricsService.recordQuery(query, searchResults.total, searchResults.processingTime);

            res.json(response);

        } catch (error) {
            logger.error('Error processing health query:', error);
            res.status(500).json({
                error: 'Failed to process health query',
                code: 'QUERY_PROCESSING_ERROR',
                message: error.message
            });
        }
    }

    async getVideoRecommendations(req, res) {
        try {
            const { videoId } = req.params;
            const { limit = 5 } = req.query;

            const recommendations = await this.semanticSearchService.getRelatedVideos(
                videoId,
                parseInt(limit)
            );

            res.json({
                videoId,
                recommendations,
                timestamp: new Date().toISOString()
            });

        } catch (error) {
            logger.error('Error getting video recommendations:', error);
            res.status(500).json({
                error: 'Failed to get video recommendations',
                code: 'RECOMMENDATIONS_ERROR',
                message: error.message
            });
        }
    }

    async handleSemanticSearch(req, res) {
        try {
            const { query, filters, options } = req.body;

            const results = await this.semanticSearchService.semanticSearch(
                query,
                filters,
                options
            );

            res.json(results);

        } catch (error) {
            logger.error('Error in semantic search:', error);
            res.status(500).json({
                error: 'Semantic search failed',
                code: 'SEMANTIC_SEARCH_ERROR',
                message: error.message
            });
        }
    }

    async start() {
        try {
            // Test database connection
            await this.db.testConnection();
            logger.info('✅ Database connection established');

            // Initialize services
            await this.healthQueryProcessor.initialize();
            await this.semanticSearchService.initialize();
            await this.metricsService.initialize();

            // Start the server
            this.server = this.app.listen(PORT, () => {
                logger.info(`🚀 MCP Server running on port ${PORT}`);
                logger.info(`📊 Metrics available at http://localhost:${PORT}/api/metrics`);
                logger.info(`🏥 Health API at http://localhost:${PORT}/api/health`);
            });

            // Graceful shutdown handling
            process.on('SIGTERM', this.shutdown.bind(this));
            process.on('SIGINT', this.shutdown.bind(this));

        } catch (error) {
            logger.error('Failed to start MCP Server:', error);
            process.exit(1);
        }
    }

    async shutdown() {
        logger.info('🛑 Shutting down MCP Server...');

        if (this.server) {
            this.server.close(() => {
                logger.info('✅ HTTP server closed');
            });
        }

        // Close database connections
        await this.db.close();
        
        // Cleanup services
        await this.metricsService.cleanup();

        logger.info('✅ MCP Server shutdown complete');
        process.exit(0);
    }
}

// Start the server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const server = new MCPServer();
    server.start().catch(error => {
        console.error('Failed to start server:', error);
        process.exit(1);
    });
}

export { MCPServer };
