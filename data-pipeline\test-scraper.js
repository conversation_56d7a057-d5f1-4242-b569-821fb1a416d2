#!/usr/bin/env node

import { VideoScraper } from './src/scrapers/videoScraper.js';
import { createLogger } from './src/utils/logger.js';
import dotenv from 'dotenv';

dotenv.config();

const logger = createLogger('TestScraper');

async function testVideoScraper() {
    logger.info('🧪 Testing Video Scraper...');
    
    try {
        // Check environment variables
        if (!process.env.APIFY_API_TOKEN) {
            throw new Error('APIFY_API_TOKEN not found in environment variables');
        }
        
        if (!process.env.DATABASE_URL) {
            throw new Error('DATABASE_URL not found in environment variables');
        }
        
        logger.info('✅ Environment variables found');
        
        // Test database connection
        const { DatabaseManager } = await import('./src/utils/database.js');
        const db = new DatabaseManager();
        
        try {
            await db.testConnection();
            logger.info('✅ Database connection successful');
        } catch (error) {
            logger.error('❌ Database connection failed:', error.message);
            logger.info('💡 Make sure PostgreSQL is running and DATABASE_URL is correct');
            return;
        }
        
        // Test video scraper initialization
        const scraper = new VideoScraper();
        logger.info('✅ Video scraper initialized');
        
        // Get current stats
        const stats = await scraper.getScrapingStats();
        if (stats) {
            logger.info('📊 Current database stats:');
            logger.info(`   Videos: ${stats.totalVideos}`);
            logger.info(`   Latest: ${stats.latestVideo}`);
            logger.info(`   Total hours: ${Math.round(stats.totalDuration / 3600)}`);
        } else {
            logger.info('📊 No videos in database yet');
        }
        
        logger.info('🎉 All tests passed! Ready to scrape videos.');
        logger.info('💡 Run "npm run scrape:videos" to start scraping');
        
        await db.close();
        
    } catch (error) {
        logger.error('❌ Test failed:', error.message);
        logger.error('🔧 Please check your configuration and try again');
    }
}

testVideoScraper();
